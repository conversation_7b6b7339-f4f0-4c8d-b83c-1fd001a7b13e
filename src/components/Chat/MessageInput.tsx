import React, { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Send, Paperclip, Smile } from 'lucide-react';
import type { MessageInputProps } from '../../types/chat';
import clsx from 'clsx';

// Form validation schema
const messageSchema = z.object({
  message: z.string()
    .min(1, 'Message cannot be empty')
    .max(2000, 'Message is too long')
    .trim(),
});

type MessageFormData = z.infer<typeof messageSchema>;

const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = 'Type your message...',
  maxLength = 2000,
  className,
  children,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      message: '',
    },
  });
  
  const messageValue = watch('message');
  const characterCount = messageValue?.length || 0;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;
  
  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;
    
    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    const maxHeight = 120; // Max height in pixels
    
    if (scrollHeight > maxHeight) {
      textarea.style.height = `${maxHeight}px`;
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.height = `${scrollHeight}px`;
      textarea.style.overflowY = 'hidden';
    }
    
    setIsExpanded(scrollHeight > 40); // Single line height
  };
  
  useEffect(() => {
    adjustTextareaHeight();
  }, [messageValue]);
  
  const onSubmit = async (data: MessageFormData) => {
    if (disabled || isOverLimit || !data.message.trim()) return;
    
    try {
      await onSendMessage(data.message.trim());
      reset();
      setIsExpanded(false);
      
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to send message:', error);
    }
  };
  
  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit(onSubmit)();
    }
  };
  
  const handlePaste = (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
    // Handle file paste in the future
    const items = event.clipboardData?.items;
    if (items) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf('image') !== -1) {
          // Handle image paste in the future
          // eslint-disable-next-line no-console
          console.log('Image pasted:', item);
        }
      }
    }
  };
  
  const canSend = messageValue?.trim() && !disabled && !isOverLimit && !isSubmitting;
  
  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={clsx(
        'message-input',
        {
          'message-input--expanded': isExpanded,
          'message-input--disabled': disabled,
          'message-input--error': errors.message || isOverLimit,
        },
        className
      )}
    >
      <div className="message-input__container">
        <div className="message-input__field-wrapper">
          <textarea
            {...register('message')}
            ref={textareaRef}
            placeholder={placeholder}
            disabled={disabled}
            maxLength={maxLength}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            className="message-input__field"
            rows={1}
            aria-label="Type your message"
            aria-describedby={
              errors.message ? 'message-error' : 
              isNearLimit ? 'character-count' : undefined
            }
          />
          
          <div className="message-input__actions">
            <button
              type="button"
              className="message-input__action"
              aria-label="Attach file"
              disabled={disabled}
            >
              <Paperclip size={18} />
            </button>
            
            <button
              type="button"
              className="message-input__action"
              aria-label="Add emoji"
              disabled={disabled}
            >
              <Smile size={18} />
            </button>
            
            <button
              type="submit"
              className={clsx(
                'message-input__send',
                {
                  'message-input__send--active': canSend,
                }
              )}
              disabled={!canSend}
              aria-label="Send message"
            >
              <Send size={18} />
            </button>
          </div>
        </div>
        
        <div className="message-input__footer">
          {errors.message && (
            <div id="message-error" className="message-input__error">
              {errors.message.message}
            </div>
          )}
          
          {isNearLimit && (
            <div
              id="character-count"
              className={clsx(
                'message-input__character-count',
                {
                  'message-input__character-count--warning': isNearLimit && !isOverLimit,
                  'message-input__character-count--error': isOverLimit,
                }
              )}
            >
              {characterCount}/{maxLength}
            </div>
          )}
          
          {!errors.message && !isNearLimit && (
            <div className="message-input__hint">
              Press Enter to send, Shift+Enter for new line
            </div>
          )}
        </div>
      </div>
      
      {children}
    </form>
  );
};

export default MessageInput;
